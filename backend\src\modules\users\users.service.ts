import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import * as bcrypt from 'bcryptjs';
import { User } from './entities/user.entity';
import { UserDepartment } from './entities/user-department.entity';
import { Organization } from '@/modules/organizations/entities/organization.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { QueryUserDto } from './dto/query-user.dto';
import { PaginationResult } from '@/common/dto/pagination.dto';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(UserDepartment)
    private userDepartmentRepository: Repository<UserDepartment>,
    @InjectRepository(Organization)
    private organizationRepository: Repository<Organization>,
  ) {}

  async create(createUserDto: CreateUserDto, currentUserId?: number): Promise<User> {
    // 检查用户名是否已存在
    const existingUser = await this.userRepository.findOne({
      where: { username: createUserDto.username },
    });
    if (existingUser) {
      throw new ConflictException('用户名已存在');
    }

    // 检查工号是否已存在
    if (createUserDto.employeeId) {
      const existingEmployee = await this.userRepository.findOne({
        where: { employeeId: createUserDto.employeeId },
      });
      if (existingEmployee) {
        throw new ConflictException('工号已存在');
      }
    }

    // 如果提供了密码，则加密密码；否则，使用默认密码
    let hashedPassword = ' ';
    if (createUserDto.password) {
      hashedPassword = await bcrypt.hash(createUserDto.password, 10);
    } else {
      // 如果没有提供密码，则生成一个默认密码
      hashedPassword = await bcrypt.hash('123456', 10); // 默认密码为 '123456'
    }

    // 提取departments字段，其余字段用于创建用户
    const { departments, ...userDto } = createUserDto;

    const user = this.userRepository.create({
      ...userDto,
      password: hashedPassword,
      createdBy: currentUserId,
    });

    const savedUser = await this.userRepository.save(user);

    // 处理多部门分配
    if (departments && departments.length > 0) {
      await this.saveDepartments(savedUser.id, departments);
    }

    return savedUser;
  }

  async findAll(queryDto: QueryUserDto): Promise<PaginationResult<User>> {
    const { page = 1, pageSize = 10, username, realName, status, departmentId, positionId } = queryDto;

    const queryBuilder = this.userRepository.createQueryBuilder('user')
      .leftJoinAndSelect('user.department', 'department')
      .leftJoinAndSelect('user.position', 'position')
      .leftJoinAndSelect('user.roles', 'roles');

    if (username) {
      queryBuilder.andWhere('user.username LIKE :username', { username: `%${username}%` });
    }

    if (realName) {
      queryBuilder.andWhere('user.realName LIKE :realName', { realName: `%${realName}%` });
    }

    if (status !== undefined) {
      queryBuilder.andWhere('user.status = :status', { status });
    }

    if (departmentId) {
      // 获取组织表名
      const organizationTableName = this.organizationRepository.metadata.tableName;

      // 构建子查询，获取部门及其所有子部门的 ID
      const departmentSubQuery = `
        WITH RECURSIVE dept_tree AS (
          -- 基础查询：获取指定的部门
          SELECT id, parent_id
          FROM ${organizationTableName}
          WHERE id = :departmentId

          UNION ALL

          -- 递归查询：获取子部门
          SELECT o.id, o.parent_id
          FROM ${organizationTableName} o
          INNER JOIN dept_tree dt ON o.parent_id = dt.id
        )
        SELECT id FROM dept_tree
      `;

      // 使用子查询结果作为 IN 条件
      queryBuilder.andWhere(`user.departmentId IN (${departmentSubQuery})`, {
        departmentId
      });
    }

    if (positionId) {
      queryBuilder.andWhere('user.positionId = :positionId', { positionId });
    }

    const [list, total] = await queryBuilder
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .getManyAndCount();

    return new PaginationResult(list, total, page, pageSize);
  }

  async findOne(id: number): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['department', 'position', 'roles', 'userDepartments', 'userDepartments.department', 'userDepartments.position'],
    });

    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    return user;
  }

  async findById(id: number): Promise<User | null> {
    return this.userRepository.findOne({
      where: { id },
      relations: ['department', 'position', 'roles', 'userDepartments', 'userDepartments.department', 'userDepartments.position'],
    });
  }

  async findByUsername(username: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { username },
      relations: ['department', 'position', 'roles', 'userDepartments', 'userDepartments.department', 'userDepartments.position'],
    });
  }

  async findByPhone(phone: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { phone },
      relations: ['department', 'position', 'roles', 'userDepartments', 'userDepartments.department', 'userDepartments.position'],
    });
  }

  async update(id: number, updateUserDto: UpdateUserDto, currentUserId?: number): Promise<User> {
    const user = await this.findOne(id);

    // 检查工号是否已存在（排除当前用户）
    if (updateUserDto.employeeId && updateUserDto.employeeId !== user.employeeId) {
      const existingEmployee = await this.userRepository.findOne({
        where: { employeeId: updateUserDto.employeeId },
      });
      if (existingEmployee && existingEmployee.id !== id) {
        throw new ConflictException('工号已存在');
      }
    }

    // 提取departments字段，其余字段用于更新用户
    const { departments, ...restUpdateUserDto } = updateUserDto;

    Object.assign(user, {
      ...restUpdateUserDto,
      updatedBy: currentUserId,
    });

    const savedUser = await this.userRepository.save(user);

    // 处理多部门分配
    if (departments !== undefined) {
      await this.saveDepartments(savedUser.id, departments);
    }

    return savedUser;
  }

  async remove(id: number): Promise<void> {
    const user = await this.findOne(id);
    await this.userRepository.remove(user);
  }

  async batchRemove(ids: number[]): Promise<void> {
    await this.userRepository.delete(ids);
  }

  async resetPassword(id: number, newPassword: string, currentUserId?: number): Promise<void> {
    const user = await this.findOne(id);
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    
    user.password = hashedPassword;
    user.updatedBy = currentUserId;
    
    await this.userRepository.save(user);
  }

  async updatePassword(id: number, newPassword: string, currentUserId?: number): Promise<void> {
    const user = await this.findOne(id);
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    
    user.password = hashedPassword;
    user.updatedBy = currentUserId;
    
    await this.userRepository.save(user);
  }

  async updateStatus(id: number, status: number, currentUserId?: number): Promise<void> {
    const user = await this.findOne(id);
    
    user.status = status;
    user.updatedBy = currentUserId;
    
    await this.userRepository.save(user);
  }

  async updateLastLoginTime(id: number): Promise<void> {
    await this.userRepository.update(id, {
      lastLoginTime: new Date(),
    });
  }

  async validatePassword(user: User, password: string): Promise<boolean> {
    return bcrypt.compare(password, user.password);
  }

  async batchUpdateStatus(ids: number[], status: number, currentUserId?: number): Promise<void> {
    await this.userRepository.update(ids, {
      status,
      updatedBy: currentUserId,
    });
  }

  async batchResetPassword(ids: number[], newPassword: string, currentUserId?: number): Promise<void> {
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    await this.userRepository.update(ids, {
      password: hashedPassword,
      updatedBy: currentUserId,
    });
  }

  async assignRoles(userId: number, roleIds: number[], currentUserId?: number): Promise<void> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['roles'],
    });

    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 这里需要根据实际的角色实体来实现
    // 暂时留空，需要角色管理模块完成后再实现
    user.updatedBy = currentUserId;
    await this.userRepository.save(user);
  }

  async batchAssignRoles(userIds: number[], roleIds: number[], currentUserId?: number): Promise<void> {
    // 这里需要根据实际的角色实体来实现
    // 暂时留空，需要角色管理模块完成后再实现
    await this.userRepository.update(userIds, {
      updatedBy: currentUserId,
    });
  }

  /**
   * 保存用户的部门分配
   */
  private async saveDepartments(userId: number, departments: Array<{ departmentId?: number; positionId?: number }>): Promise<void> {
    // 删除现有的部门分配
    await this.userDepartmentRepository.delete({ userId });

    // 创建新的部门分配
    const userDepartments = departments
      .filter(dept => dept.departmentId || dept.positionId) // 过滤掉空的分配
      .map(dept => this.userDepartmentRepository.create({
        userId,
        departmentId: dept.departmentId,
        positionId: dept.positionId,
      }));

    if (userDepartments.length > 0) {
      await this.userDepartmentRepository.save(userDepartments);
    }
  }
}
