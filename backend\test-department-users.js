/**
 * 测试部门用户查询功能
 * 验证 /api/users?departmentId=X 能够查询到该部门及其子部门的所有用户
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

// 测试用户凭据
const TEST_USER = {
  account: 'admin',
  password: 'admin123'
};

async function testDepartmentUsersQuery() {
  try {
    console.log('🏢 开始测试部门用户查询功能...\n');

    // 1. 登录获取token
    console.log('1. 正在登录...');
    const loginResponse = await axios.post(`${BASE_URL}/system/login`, TEST_USER);
    
    if (loginResponse.data.code !== 200) {
      throw new Error('登录失败: ' + loginResponse.data.message);
    }
    
    const token = loginResponse.data.data.token;
    console.log('✅ 登录成功');

    const headers = {
      'Authorization': `Bearer ${token}`
    };

    // 2. 获取组织架构
    console.log('\n2. 获取组织架构...');
    const orgsResponse = await axios.get(`${BASE_URL}/organizations/tree`, { headers });
    
    if (orgsResponse.data.code !== 200) {
      throw new Error('获取组织架构失败: ' + orgsResponse.data.message);
    }

    const organizations = orgsResponse.data.data;
    console.log(`📋 获取到 ${organizations.length} 个顶级组织`);

    // 打印组织架构
    function printOrgTree(orgs, level = 0) {
      orgs.forEach(org => {
        const indent = '  '.repeat(level);
        console.log(`${indent}├─ ${org.name} (ID: ${org.id})`);
        if (org.children && org.children.length > 0) {
          printOrgTree(org.children, level + 1);
        }
      });
    }

    console.log('\n组织架构:');
    printOrgTree(organizations);

    // 3. 获取所有用户（不带部门过滤）
    console.log('\n3. 获取所有用户...');
    const allUsersResponse = await axios.get(`${BASE_URL}/users`, { headers });
    
    if (allUsersResponse.data.code !== 200) {
      throw new Error('获取所有用户失败: ' + allUsersResponse.data.message);
    }

    const allUsers = allUsersResponse.data.data.list;
    console.log(`👥 总共有 ${allUsers.length} 个用户`);

    // 按部门分组显示用户
    const usersByDept = {};
    allUsers.forEach(user => {
      const deptId = user.departmentId || 'null';
      const deptName = user.department?.name || '未分配部门';
      if (!usersByDept[deptId]) {
        usersByDept[deptId] = {
          name: deptName,
          users: []
        };
      }
      usersByDept[deptId].users.push(user);
    });

    console.log('\n用户按部门分布:');
    Object.entries(usersByDept).forEach(([deptId, dept]) => {
      console.log(`  ${dept.name} (ID: ${deptId}): ${dept.users.length} 个用户`);
      dept.users.forEach(user => {
        console.log(`    - ${user.realName} (${user.username})`);
      });
    });

    // 4. 测试特定部门的用户查询
    const testDepartmentId = 7; // 根据实际情况调整
    console.log(`\n4. 测试查询部门 ID ${testDepartmentId} 及其子部门的用户...`);
    
    const deptUsersResponse = await axios.get(`${BASE_URL}/users?departmentId=${testDepartmentId}`, { headers });
    
    if (deptUsersResponse.data.code !== 200) {
      throw new Error('查询部门用户失败: ' + deptUsersResponse.data.message);
    }

    const deptUsers = deptUsersResponse.data.data.list;
    console.log(`🎯 部门 ${testDepartmentId} 及其子部门共有 ${deptUsers.length} 个用户:`);
    
    deptUsers.forEach(user => {
      const deptName = user.department?.name || '未分配部门';
      console.log(`  - ${user.realName} (${user.username}) - 所属部门: ${deptName} (ID: ${user.departmentId})`);
    });

    // 5. 验证结果
    console.log('\n5. 验证查询结果...');
    
    // 找到目标部门及其子部门
    function findDepartmentAndChildren(orgs, targetId) {
      const result = [];
      
      function traverse(orgs) {
        orgs.forEach(org => {
          if (org.id === targetId) {
            result.push(org.id);
            if (org.children) {
              function addChildren(children) {
                children.forEach(child => {
                  result.push(child.id);
                  if (child.children) {
                    addChildren(child.children);
                  }
                });
              }
              addChildren(org.children);
            }
          } else if (org.children) {
            traverse(org.children);
          }
        });
      }
      
      traverse(orgs);
      return result;
    }

    const targetDeptIds = findDepartmentAndChildren(organizations, testDepartmentId);
    console.log(`📍 目标部门及其子部门 IDs: [${targetDeptIds.join(', ')}]`);

    // 验证返回的用户是否都属于目标部门或其子部门
    let isValid = true;
    deptUsers.forEach(user => {
      if (user.departmentId && !targetDeptIds.includes(user.departmentId)) {
        console.log(`❌ 用户 ${user.realName} 的部门 ID ${user.departmentId} 不在目标范围内`);
        isValid = false;
      }
    });

    if (isValid) {
      console.log('✅ 查询结果验证通过：所有返回的用户都属于目标部门或其子部门');
    } else {
      console.log('❌ 查询结果验证失败：存在不属于目标部门范围的用户');
    }

    // 6. 测试其他部门
    console.log('\n6. 测试其他部门...');
    const otherTestIds = [1, 2, 3]; // 可以根据实际情况调整
    
    for (const deptId of otherTestIds) {
      try {
        const response = await axios.get(`${BASE_URL}/users?departmentId=${deptId}`, { headers });
        if (response.data.code === 200) {
          const users = response.data.data.list;
          console.log(`  部门 ${deptId}: ${users.length} 个用户`);
        }
      } catch (error) {
        console.log(`  部门 ${deptId}: 查询失败`);
      }
    }

    console.log('\n🎉 部门用户查询功能测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
    process.exit(1);
  }
}

// 运行测试
testDepartmentUsersQuery();
